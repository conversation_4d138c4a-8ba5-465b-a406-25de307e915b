<?php

namespace App\Services\Meta\WhatsApp\UseCases\Webhook;

use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Organization;
use App\Domains\WhatsApp\ChangeValue;
use App\Helpers\DBLog;
use App\UseCases\WhatsAppWebhookLog\LogWebhookEvent;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;

class ProcessWebhook
{

    /**
     * Process webhook data for a specific organization and phone number
     *
     * @param ChangeValue $changeValue
     * @param Organization $organization
     * @param PhoneNumber $phoneNumber
     * @return array|null
     * @throws BindingResolutionException
     * @throws Exception
     * @throws \Throwable
     */
    public function perform(
        ChangeValue $changeValue,
        Organization $organization,
        PhoneNumber $phoneNumber
    ): ?array {

        /** @var LogWebhookEvent $logWebhookEvent */
        $logWebhookEvent = app()->make(LogWebhookEvent::class);

        $webhookLog = $logWebhookEvent->perform(
            $organization->id,
            $phoneNumber->whatsapp_phone_number_id,
            $changeValue->getEventType(),
            $changeValue->toArray()
        );

        $webhookLogId = $webhookLog->id ?? null;

        DBLog::logInfo(
            "Processing webhook message", "ProcessWebhook::perform", $organization->id, null,
            [
                'phone_number_id' => $phoneNumber->id, 'phone_number_id_whatsapp_id' => $phoneNumber->whatsapp_phone_number_id,
                'webhook_log_id' => $webhookLogId, 'flow_id' => $phoneNumber->flow_id,
                'has_messages' => $changeValue->hasMessages(), 'has_statuses' => $changeValue->hasStatuses(), 'event_type' => $changeValue->getEventType()
            ]
        );

        if ($changeValue->hasMessages() && $changeValue->hasStatuses()) {
            DBLog::logError(
                "Webhook has both messages and statuses, should never happen ignoring", "ProcessWebhook::perform", $organization->id, null,
                [
                    'phone_number_id' => $phoneNumber->id, 'phone_number_id_whatsapp_id' => $phoneNumber->whatsapp_phone_number_id,
                    'webhook_log_id' => $webhookLogId, 'flow_id' => $phoneNumber->flow_id,
                    'has_messages' => $changeValue->hasMessages(), 'has_statuses' => $changeValue->hasStatuses(), 'event_type' => $changeValue->getEventType()
                ]
            );
            return null;
        }

        if ($changeValue->hasMessages()) {
            /** @var ProcessWebhookMessage $processWebhookMessage */
            $processWebhookMessage = app()->make(ProcessWebhookMessage::class);
            return $processWebhookMessage->perform($changeValue, $organization, $phoneNumber, $webhookLogId);
        } elseif ($changeValue->hasStatuses()) {
            /** @var ProcessWebhookMessageStatusUpdate $processWebhookStatus */
            $processWebhookStatus = app()->make(ProcessWebhookMessageStatusUpdate::class);
            return $processWebhookStatus->perform($changeValue, $organization, $phoneNumber);
        }

        return null;
    }
}
