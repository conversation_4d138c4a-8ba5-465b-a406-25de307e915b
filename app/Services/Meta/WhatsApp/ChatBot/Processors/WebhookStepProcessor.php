<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Domains\ChatBot\Step;
use App\Domains\ChatBot\Conversation;
use App\Domains\ChatBot\Interaction;
use App\Services\Meta\WhatsApp\ChatBot\Domains\StepProcessorResult;
use App\Services\Meta\WhatsApp\ChatBot\Factories\StepProcessorResultFactory;
use App\Services\VariableSubstitution\VariableSubstitutionService;
use App\Enums\ChatBot\StepType;
use App\Helpers\DBLog;
use Illuminate\Support\Facades\Http;

/**
 * Processor for WEBHOOK type steps
 *
 * Handles webhook steps that make calls to external APIs or services.
 * This enables integration with third-party systems and services
 * during the conversation flow.
 *
 * @package App\Services\Meta\WhatsApp\ChatBot\Processors
 */
class WebhookStepProcessor implements StepProcessorInterface
{
    protected VariableSubstitutionService $variableSubstitutionService;
    protected StepProcessorResultFactory $resultFactory;

    public function __construct(
        VariableSubstitutionService $variableSubstitutionService,
        StepProcessorResultFactory $resultFactory
    ) {
        $this->variableSubstitutionService = $variableSubstitutionService;
        $this->resultFactory = $resultFactory;
    }
    /**
     * Process a webhook step
     *
     * Webhook steps make HTTP calls to external endpoints and can
     * process the responses to determine next actions or update
     * conversation state.
     *
     * @param Step $step
     * @param Interaction $interaction
     * @param Conversation $conversation
     * @return StepProcessorResult
     */
    public function process(
        Step $step,
        Interaction $interaction,
        Conversation $conversation
    ): StepProcessorResult {
        // Get webhook configuration from step
        $webhookConfig = $this->getWebhookConfiguration($step);

        if (!$webhookConfig) {
            return $this->resultFactory->createWebhookFailed(
                step: $step,
                error: 'Webhook configuration not found'
            );
        }

        try {
            // Execute webhook call
            $webhookResult = $this->executeWebhookCall($webhookConfig, $conversation, $interaction);

            return $this->resultFactory->createWebhookExecuted(
                step: $step,
                webhook_result: $webhookResult
            );
        } catch (\Exception $e) {
            DBLog::logError(
                'Webhook step execution failed',
                'WebhookStepProcessor',
                $conversation->organization_id ?? null,
                null,
                [
                    'step_id' => $step->id,
                    'step_identifier' => $step->step,
                    'webhook_url' => $webhookConfig['url'] ?? 'unknown',
                    'error' => $e->getMessage(),
                    'conversation_id' => $conversation->id
                ]
            );

            return $this->resultFactory->createWebhookFailed(
                step: $step,
                error: $e->getMessage(),
            );
        }
    }

    /**
     * Check if this processor can handle the given step
     *
     * @param Step $step
     * @return bool
     */
    public function canProcess(Step $step): bool
    {
        // Ensure step_type is set from legacy fields if needed
        $step->setStepTypeFromLegacyFields();

        return $step->step_type === StepType::WEBHOOK;
    }

    /**
     * Get supported step types
     *
     * @return array
     */
    public function getSupportedStepTypes(): array
    {
        return [StepType::WEBHOOK->value];
    }

    /**
     * Get webhook configuration from step
     *
     * @param Step $step
     * @return array|null
     */
    protected function getWebhookConfiguration(Step $step): ?array
    {
        if (!$step->json) {
            return null;
        }

        $jsonData = json_decode($step->json, true);
        if (!is_array($jsonData) || !isset($jsonData['url'])) {
            return null;
        }

        return $jsonData;
    }

    /**
     * Execute webhook call
     *
     * @param array $webhookConfig
     * @param Conversation $conversation
     * @param Interaction $interaction
     * @return array
     * @throws \Exception
     */
    protected function executeWebhookCall(
        array $webhookConfig,
        Conversation $conversation,
        Interaction $interaction
    ): array {
        try {
            // 1. Prepare the payload with conversation and interaction data
            $payload = $this->prepareWebhookPayload($conversation, $interaction);

            // 2. Make HTTP request to the configured URL
            $url = $webhookConfig['url'];
            $method = strtoupper($webhookConfig['method'] ?? 'POST');
            $timeout = $webhookConfig['timeout'] ?? 30;

            $response = Http::timeout($timeout)->$method($url, $payload);

            // 3. Handle response and extract relevant data
            $statusCode = $response->status();
            $responseData = $response->json();

            return [
                'success' => $response->successful(),
                'message' => $response->successful() ? 'Webhook call completed successfully' : 'Webhook call failed',
                'data' => [
                    'url' => $url,
                    'method' => $method,
                    'status_code' => $statusCode,
                    'response' => $responseData,
                    'payload_sent' => $payload
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Webhook call failed: ' . $e->getMessage(),
                'data' => [
                    'url' => $webhookConfig['url'] ?? 'unknown',
                    'method' => $webhookConfig['method'] ?? 'POST',
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * Prepare webhook payload with conversation and interaction data
     */
    protected function prepareWebhookPayload(
        Conversation $conversation,
        Interaction $interaction
    ): array {
        return [
            'conversation' => [
                'id' => $conversation->id,
                'client_id' => $conversation->client_id,
                'flow_id' => $conversation->flow_id,
                'current_step_id' => $conversation->current_step_id,
                'is_finished' => $conversation->is_finished,
                'client' => $conversation->client?->toArray(),
            ],
            'interaction' => [
                'id' => $interaction->id,
                'message' => $interaction->message,
                'whatsapp_message_id' => $interaction->whatsapp_message_id,
                'whatsapp_message_type' => $interaction->whatsapp_message_type,
                'whatsapp_raw_data' => $interaction->whatsapp_raw_data,
            ],
            'timestamp' => now()->toISOString(),
            'organization_id' => $conversation->organization_id,
        ];
    }
}
