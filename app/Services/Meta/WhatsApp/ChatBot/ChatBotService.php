<?php

namespace App\Services\Meta\WhatsApp\ChatBot;

use App\Domains\ChatBot\PhoneNumber;
use App\Helpers\DBLog;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WebHookData;
use App\Services\Meta\WhatsApp\ChatBot\Domains\MessageData;
use App\Services\Meta\WhatsApp\ChatBot\Factories\MessageDataFactory;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Client\FindOrCreateClient;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Conversation\FinishConversation;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Conversation\MoveConversationToNextStep;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\FindOrCreateConversation;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\GetFlowByPhoneNumber;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessFlowStep;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\SendWhatsAppResponse;
use Exception;
use Throwable;

class ChatBotService
{
    protected ProcessFlowStep $processFlowStep;
    protected FindOrCreateClient $findOrCreateClient;
    protected FindOrCreateConversation $findOrCreateConversation;
    protected SendWhatsAppResponse $sendWhatsAppResponse;
    protected MessageDataFactory $messageDataFactory;
    protected GetFlowByPhoneNumber $getFlowByPhoneNumber;

    public function __construct(
        ProcessFlowStep $processFlowStep,
        FindOrCreateClient $findOrCreateClient,
        FindOrCreateConversation $findOrCreateConversation,
        SendWhatsAppResponse $sendWhatsAppResponse,
        MessageDataFactory $messageDataFactory,
        GetFlowByPhoneNumber $getFlowByPhoneNumber
    ) {
        $this->processFlowStep = $processFlowStep;
        $this->findOrCreateClient = $findOrCreateClient;
        $this->findOrCreateConversation = $findOrCreateConversation;
        $this->sendWhatsAppResponse = $sendWhatsAppResponse;
        $this->messageDataFactory = $messageDataFactory;
        $this->getFlowByPhoneNumber = $getFlowByPhoneNumber;
    }

    /**
     * Main entry point for processing WhatsApp webhook messages
     *
     * @param WebHookData $webhookData
     * @param PhoneNumber $phoneNumber
     * @return array
     * @throws Exception|Throwable
     */
    public function processWebhook(WebHookData $webhookData, PhoneNumber $phoneNumber): array
    {
        try {

            DBLog::log(
                "ChatBotService::processWebhook - Processing webhook",
                "WhatsApp::ChatBotService",
                null,
                null,
                ['webhook_data' => $webhookData->toArray()]
            );

            $messageData = $this->messageDataFactory->buildFromWebHookData($webhookData);

            $flow = $this->getFlowByPhoneNumber->perform($phoneNumber);

            $client = $this->findOrCreateClient->perform($messageData, $phoneNumber->organization);

            $conversation = $this->findOrCreateConversation->perform($messageData, $client, $phoneNumber);

            $stepResult = $this->processFlowStep->perform($conversation, $messageData, $flow);

            if ($stepResult->shouldFinishConversation()) {
                /** @var FinishConversation $finishConversation */
                $finishConversation = app()->make(FinishConversation::class);
                $finishConversation->perform($conversation, $stepResult);
            }
            if ($stepResult->shouldMoveToNext()) {
                /** @var MoveConversationToNextStep $moveUseCase */
                $moveUseCase = app()->make(MoveConversationToNextStep::class);
                $moveUseCase->perform($conversation, $stepResult);
            }

            $response = $this->sendWhatsAppResponse->perform($stepResult, $conversation);

            return [
                'success' => true,
                'conversation_id' => $conversation->id,
                'client_id' => $client->id,
                'step_result' => $stepResult,
                'response' => $response
            ];

        } catch (Exception $e) {
            DBLog::logError(
                "ChatBotService::processWebhook - Error processing webhook: " . $e->getMessage(),
                "WhatsApp::ChatBotService",
                null,
                null,
                ['webhook_data' => $webhookData, 'error' => $e->getMessage()]
            );

            throw $e;
        }
    }
}
