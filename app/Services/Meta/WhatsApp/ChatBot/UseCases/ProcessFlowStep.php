<?php

namespace App\Services\Meta\WhatsApp\ChatBot\UseCases;

use App\Domains\ChatBot\Flow;
use App\Helpers\DBLog;
use App\Domains\ChatBot\Conversation;
use App\Domains\ChatBot\Interaction;
use App\Services\Meta\WhatsApp\ChatBot\Domains\MessageData;
use App\Services\Meta\WhatsApp\ChatBot\Domains\StepProcessorResult;
use App\Repositories\InteractionRepository;
use App\Factories\ChatBot\InteractionFactory;
use App\Services\Meta\WhatsApp\ChatBot\Services\ConditionalNavigationService;
use App\Services\Meta\WhatsApp\ChatBot\Services\ErrorHandlerService;
use App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorFactory;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessStepResponse;
use App\Domains\ChatBot\Step;
use App\Enums\ChatBot\InteractionState;
use Exception;

class ProcessFlowStep
{
    protected InteractionRepository $interactionRepository;
    protected InteractionFactory $interactionFactory;
    protected ConditionalNavigationService $conditionalNavigationService;
    protected ErrorHandlerService $errorHandlerService;
    protected StepProcessorFactory $stepProcessorFactory;
    protected ProcessStepResponse $processStepResponse;

    public function __construct(
        InteractionRepository $interactionRepository,
        InteractionFactory $interactionFactory,
        ConditionalNavigationService $conditionalNavigationService,
        ErrorHandlerService $errorHandlerService,
        StepProcessorFactory $stepProcessorFactory,
        ProcessStepResponse $processStepResponse
    ) {
        $this->interactionRepository = $interactionRepository;
        $this->interactionFactory = $interactionFactory;
        $this->conditionalNavigationService = $conditionalNavigationService;
        $this->errorHandlerService = $errorHandlerService;
        $this->stepProcessorFactory = $stepProcessorFactory;
        $this->processStepResponse = $processStepResponse;
    }

    /**
     * Process current flow step and determine next action
     *
     * @param Conversation $conversation
     * @param MessageData $messageData
     * @param Flow $flow
     * @return StepProcessorResult
     */
    public function perform(Conversation $conversation, MessageData $messageData, Flow $flow): StepProcessorResult
    {
        DBLog::logInfo(
            "Processing flow step", "ProcessFlowStep::perform", null, null,
            ['flow_id' => $flow->id, 'message_data' => $messageData->toArray(), 'conversation' => $conversation->toArray()]
        );
        $interaction = null;
        $currentStep = null;

        try {
            $interaction = $this->interactionRepository->save(
                $this->interactionFactory->buildFromWebhookData($messageData, $conversation),
                $conversation->organization_id
            );

            $currentStep = $conversation->current_step;
            if (!$currentStep) {
                throw new Exception('No current step found for conversation');
            }

            $stepResult = $this->processStepByType($currentStep, $interaction, $conversation);
            DBLog::log(
                "Step processed",
                "WhatsApp::ProcessFlowStep",
                $conversation->organization_id ?? null,
                null,
                ['step_result' => $stepResult->toArray()]
            );

            $stepResult = $this->applyConditionalNavigation($stepResult, $currentStep, $interaction);
            DBLog::log(
                "Conditional navigation applied",
                "WhatsApp::ProcessFlowStep",
                $conversation->organization_id ?? null,
                null,
                ['step_result' => $stepResult->toArray()]
            );

            $interaction->result = json_encode($stepResult->toArray());
            $this->interactionRepository->save($interaction, $conversation->organization_id);

            $stepResult->interaction = $interaction;

            DBLog::log(
                "Step result finished",
                "WhatsApp::ProcessFlowStep::stepResult",
                $conversation->organization_id ?? null,
                null,
                ['stepResult' => $stepResult->toArray()]
            );

            return $stepResult;

        } catch (\Exception $e) {
            $errorResult = $this->errorHandlerService->handleError(
                $e,
                null,
                $conversation,
                $interaction,
                $currentStep
            );

            return StepProcessorResult::error(
                type: $errorResult['type'] ?? 'unknown',
                step_id: $currentStep?->id ?? 0,
                action: $errorResult['action'] ?? 'unknown',
                error: $errorResult['error'] ?? 'unknown',
                current_step: $currentStep ?? null
            );
        }
    }


    /**
     * Process step based on its type using Strategy Pattern
     * @throws Exception
     */
    protected function processStepByType(
        Step $step,
        Interaction $interaction,
        Conversation $conversation
    ): StepProcessorResult {
        try {
            $processor = $this->stepProcessorFactory->getProcessor($step);
            DBLog::log(
                "Step processor found",
                "WhatsApp::ProcessFlowStep",
                $conversation->organization_id ?? null,
                null,
                ['step_id' => $step->id, 'step_type' => $step->step_type?->value ?? 'unknown']
            );
            return $processor->process($step, $interaction, $conversation);
        } catch (\InvalidArgumentException $e) {
            \Log::warning('No processor found for step type, using default processing', [
                'step_id' => $step->id,
                'step_type' => $step->step_type?->value ?? 'unknown',
                'error' => $e->getMessage()
            ]);

            return $this->processDefaultStep($step);
        }
    }

    /**
     * Process default step when no specific processor is found
     *
     * This is a fallback method that handles steps when:
     * - Step type is not recognized by StepProcessorFactory
     * - Step type is null or invalid
     * - StepProcessor throws an exception
     *
     * Used in scenarios like:
     * - Legacy steps without proper step_type
     * - Corrupted step data
     * - Missing processor implementations
     */
    protected function processDefaultStep(Step $step): StepProcessorResult
    {
        return StepProcessorResult::success(
            type: 'default',
            step_id: $step->id,
            action: 'default_processing',
            next_step: $step->next_step,
            move_to_next: true,
            message: $this->getStepMessage($step),
            current_step: $step
        );
    }

    /**
     * Get message content for step
     */
    protected function getStepMessage(Step $step): string
    {
        // First try to get message from JSON configuration
        if ($step->json) {
            $jsonData = json_decode($step->json, true);
            if (is_array($jsonData) && isset($jsonData['message'])) {
                return $jsonData['message'];
            }
        }

        // Fallback to step name
        return $step->step ?? 'Step message not configured';
    }


    /**
     * Apply conditional navigation to step result
     *
     * This is the CORE method that handles navigation between steps based on user interaction.
     * It's called after a step is processed to determine where to go next.
     *
     * CRITICAL for flow functionality - this method:
     * 1. Checks if step should move to next (move_to_next = true)
     * 2. Determines if step has conditional navigation rules
     * 3. Finds the correct next step based on user interaction
     * 4. Updates step result with new navigation target
     *
     * Used in scenarios like:
     * - Pizzaria: User clicks "🍕 Pequena" → goes to "choose_pizza_flavor"
     * - Pizzaria: User clicks "🍅 Margherita" → goes to "collect_address"
     * - Any CONDITION step with navigation_rules
     * - Button clicks that should navigate to specific steps
     *
     * @param StepProcessorResult $stepResult Result from step processor
     * @param Step $currentStep Current step being processed
     * @param Interaction $interaction User's interaction (button click, text, etc)
     * @return StepProcessorResult Updated step result with navigation applied
     */
    protected function applyConditionalNavigation(
        StepProcessorResult $stepResult,
        Step $currentStep,
        Interaction $interaction
    ): StepProcessorResult {
        // Only apply conditional navigation if we're moving to next step
        if (!$stepResult->shouldMoveToNext()) {
            DBLog::log(
                "Conditional navigation skipped - not moving to next step",
                "WhatsApp::ProcessFlowStep",
                $interaction->conversation->organization_id ?? null,
                null,
                ['step_result' => $stepResult]
            );
            return $stepResult;
        }

        // Check if current step has conditional navigation
        if (!$this->conditionalNavigationService->hasConditionalNavigation($currentStep)) {
            DBLog::log(
                "Conditional navigation skipped - no conditional navigation rules",
                "WhatsApp::ProcessFlowStep",
                $interaction->conversation->organization_id ?? null,
                null,
                ['step_id' => $currentStep->id, 'step_identifier' => $currentStep->step]
            );
            return $stepResult;
        }

        $conditionalNextStep = $this->conditionalNavigationService->getNextStepForInteraction($currentStep, $interaction);

        if ($conditionalNextStep) {
            $stepResult = StepProcessorResult::success(
                type: $stepResult->type,
                step_id: $stepResult->step_id,
                action: $stepResult->action,
                next_step: $conditionalNextStep->id,
                move_to_next: true,
                message: $stepResult->message,
                current_step: $stepResult->current_step,
                additional_data: [
                    'conditional_navigation' => true,
                    'target_step_identifier' => $conditionalNextStep->step,
                    'original_next_step' => $stepResult->next_step
                ]
            );

            DBLog::log(
                "Conditional navigation applied successfully",
                "WhatsApp::ProcessFlowStep",
                $interaction->conversation->organization_id ?? null,
                null,
                [
                    'from_step' => $currentStep->step,
                    'to_step' => $conditionalNextStep->step,
                    'button_id' => $interaction->getButtonId(),
                    'interaction_type' => $interaction->whatsapp_message_type,
                    'step_result' => $stepResult
                ]
            );
        } else {
            DBLog::log(
                "Conditional navigation failed - no target step found",
                "WhatsApp::ProcessFlowStep",
                $interaction->conversation->organization_id ?? null,
                null,
                [
                    'current_step' => $currentStep->step,
                    'button_id' => $interaction->getButtonId(),
                    'interaction_type' => $interaction->whatsapp_message_type
                ]
            );
        }

        return $stepResult;
    }

    /**
     * NEW: Process flow step using state management system
     * This method implements the new architecture with interaction states
     */
    public function performWithStateManagement(Conversation $conversation, MessageData $messageData, Flow $flow): array
    {
        DBLog::logInfo(
            "Processing flow step with state management",
            "ProcessFlowStep::performWithStateManagement",
            $conversation->organization_id,
            null,
            [
                'conversation_id' => $conversation->id,
                'current_step_id' => $conversation->current_step_id,
                'message_type' => $messageData->type,
                'is_button_click' => $messageData->is_button_click
            ]
        );

        try {
            // Check if this is a response to an existing interaction
            $pendingInteraction = $this->interactionRepository->findPendingInteraction($conversation->id);

            if ($pendingInteraction) {
                // This is a response to a pending interaction
                DBLog::logInfo(
                    "Found pending interaction, processing response",
                    "ProcessFlowStep::performWithStateManagement",
                    $conversation->organization_id,
                    null,
                    ['pending_interaction_id' => $pendingInteraction->id]
                );

                return $this->processStepResponse->perform($messageData, $conversation);
            } else {
                // This is a new conversation or restart
                DBLog::logInfo(
                    "No pending interaction, starting new flow",
                    "ProcessFlowStep::performWithStateManagement",
                    $conversation->organization_id,
                    null,
                    ['action' => 'start_new_flow']
                );

                return $this->startNewFlow($conversation, $flow);
            }

        } catch (Exception $e) {
            DBLog::logError(
                "Failed to process flow step with state management: " . $e->getMessage(),
                "ProcessFlowStep::performWithStateManagement",
                $conversation->organization_id,
                null,
                [
                    'conversation_id' => $conversation->id,
                    'message_data' => $messageData->toArray(),
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            );

            throw $e;
        }
    }

    /**
     * Start new flow from the beginning
     */
    protected function startNewFlow(Conversation $conversation, Flow $flow): array
    {
        if (!$flow->getInitialStep()) {
            throw new Exception('Flow has no first step configured');
        }

        $conversation->current_step_id = $flow->getInitialStep()->id;

        $sendStepMessage = app()->make(SendStepMessage::class);
        $interaction = $sendStepMessage->perform($flow->getInitialStep(), $conversation);

        return [
            'success' => true,
            'action' => 'flow_started',
            'first_step_sent' => true,
            'interaction' => $interaction,
            'conversation_updated' => true
        ];
    }

    /**
     * Check if conversation should use state management
     * This allows gradual migration to the new system
     */
    public function shouldUseStateManagement(Conversation $conversation): bool
    {
        // For now, enable for all conversations
        // Later you can add conditions like:
        // - Specific flow IDs
        // - Organization settings
        // - Feature flags

        return true;
    }
}
