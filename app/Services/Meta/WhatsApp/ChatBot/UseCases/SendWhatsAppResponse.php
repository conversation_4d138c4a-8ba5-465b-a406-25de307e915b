<?php

namespace App\Services\Meta\WhatsApp\ChatBot\UseCases;

use App\Helpers\DBLog;
use App\Services\Meta\WhatsApp\ChatBot\Domains\StepProcessorResult;
use App\Domains\ChatBot\Conversation;
use App\Repositories\ConversationRepository;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\SendMessages\SendMessage;
use App\UseCases\ChatBot\Message\SaveMessageFromChatBot;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Throwable;

class SendWhatsAppResponse
{
    protected ConversationRepository $conversationRepository;

    private const array SEND_MESSAGE_ACTIONS = [
        'send_message',
        'show_options',
        'request_input',
        'input_invalid',
        'input_processed',
        'default_processing',
        'process_selection',  // ← ADICIONADO: Para cliques de botão
    ];

    public function __construct(
        ConversationRepository $conversationRepository
    ) {
        $this->conversationRepository = $conversationRepository;
    }

    /**
     * Send WhatsApp response based on step result
     *
     * @param StepProcessorResult $stepResult
     * @param Conversation $conversation
     * @return array
     * @throws BindingResolutionException|Throwable
     */
    public function perform(StepProcessorResult $stepResult, Conversation $conversation): array
    {
        try {
            $response = null;

            $shouldSendMessage = in_array(
                $stepResult->action ?? '', self::SEND_MESSAGE_ACTIONS
            ) && !empty($stepResult->message);

            if ($shouldSendMessage) {
                /** @var SendMessage $useCase */
                $useCase = app()->make(SendMessage::class);
                $response = $useCase->perform($stepResult->toArray(), $conversation);

                /** @var SaveMessageFromChatBot $saveMessageUseCase */
                $saveMessageUseCase = app()->make(SaveMessageFromChatBot::class);
                $savedMessages = $saveMessageUseCase->perform(
                    $stepResult->toArray(),
                    $conversation,
                    $response
                );

                DBLog::log(
                    "ChatBot message saved",
                    "WhatsApp::SendWhatsAppResponse",
                    $conversation->organization_id ?? null,
                    null,
                    [
                        'conversation_id' => $conversation->id,
                        'message_id' => $savedMessages['message']->id,
                        'whatsapp_message_id' => $savedMessages['whatsapp_message']?->id,
                        'step_result' => $stepResult
                    ]
                );
            }

            $sendInformation = [
                'sent' => $response !== null,
                'response' => $response,
                'conversation_updated' => $stepResult['move_to_next'] ?? false,
                'conversation_finished' => $stepResult['finish_conversation'] ?? false,
                'step_result' => $stepResult,
            ];

            DBLog::log(
                "WhatsApp response sent",
                "WhatsApp::SendWhatsAppResponse",
                $conversation->organization_id ?? null,
                null,
                [
                    'conversation_id' => $conversation->id,
                    'step_result' => $stepResult,
                    'response' => $response
                ]
            );

            return $sendInformation;

        } catch (Exception $e) {
            DBLog::logError(
                "Failed to send WhatsApp response",
                "WhatsApp::SendWhatsAppResponse",
                $conversation->organization_id ?? null,
                null,
                [
                    'conversation_id' => $conversation->id,
                    'step_result' => $stepResult,
                    'error' => $e->getMessage()
                ]
            );

            throw $e;
        }
    }

    /**
     * Get message from current step after navigation
     */
    protected function getMessageFromCurrentStep(Conversation $conversation, array $originalStepResult): array
    {
        // Se não há step atual, retorna o resultado original
        if (!$conversation->current_step) {
            return $originalStepResult;
        }

        // Pegar a mensagem do step atual
        $currentStep = $conversation->current_step;
        $message = $currentStep->component?->message ?? null;

        if ($message) {
            DBLog::logInfo(
                "Message from current step retrieved",
                "WhatsApp::SendWhatsAppResponse::getMessageFromCurrentStep",
                $conversation->organization_id ?? null,
                null,
                [
                    'conversation_id' => $conversation->id,
                    'current_step_id' => $currentStep->id,
                    'message' => $message
                ]
            );
            // Atualizar o stepResult com a mensagem do novo step
            $originalStepResult['message'] = $message;
            $originalStepResult['action'] = 'send_message';
            $originalStepResult['step_id'] = $currentStep->id;
        }

        return $originalStepResult;
    }
}
